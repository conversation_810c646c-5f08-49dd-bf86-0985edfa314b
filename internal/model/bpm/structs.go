/**
 * @note
 * struct
 *
 * <AUTHOR>
 * @date 	2025-09-03
 */
package bpm

import (
	"gorm.io/gorm"

	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
)

type BpmProcessTable struct {
	gorm.Model
	RefID      string                     `json:"refID" gorm:"column:ref_id"` // 审批所关联的资源ID
	RefType    socCommon.BpmRefType       `json:"refType" gorm:"column:ref_type"`
	WorkflowID string                     `json:"workflowID" gorm:"column:workflow_id"` // 对应的审批ID
	Initiator  string                     `json:"initiator" gorm:"column:initiator"`    // 发起人
	Detail     string                     `json:"detail" gorm:"column:detail"`
	Status     socCommon.BpmProcessStatus `json:"status" gorm:"column:status"`
}

func (t *BpmProcessTable) TableName() string {
	return socCommon.BpmProcessTableName
}
