/**
 * @note
 * filter
 *
 * <AUTHOR>
 * @date 	2025-09-03
 */
package filter

import (
	"context"
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/mysql"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"gorm.io/gorm"
)

type FilterRuleModel interface {
	// FilterRule methods
	CreateFilterRule(ctx context.Context, rule *RuleTable) (int64, error)
	DeleteFilterRule(ctx context.Context, ruleID string) error
	UpdateFilterRuleByRuleID(ctx context.Context, ruleID string, businessID *string, tenantID *TenantIDArray, name *string, desc *string, ruleType *socCommon.FilterRuleType, filterConfig *FilterRuleConfig, dedupConfig *DeduplicationRuleConfig, status *socCommon.FilterRuleStatus, version *int64) error
	QueryFilterRuleBySeveralConditions(ctx context.Context, filter FilterRuleQueryFilter) ([]*RuleTable, error)
	QueryFilterRuleCountBySeveralConditions(ctx context.Context, filter FilterRuleQueryFilter) (int64, error)
	QueryLatestFilterRule(ctx context.Context, filter FilterRuleQueryFilter) ([]*RuleTable, error)
	QueryLatestFilterRuleCount(ctx context.Context, filter FilterRuleQueryFilter) (int64, error)
}

type FilterRuleModelImpl struct{}

var DefaultService FilterRuleModel = &FilterRuleModelImpl{}

func (m *FilterRuleModelImpl) getDB(ctx context.Context) (db *gorm.DB, err error) {
	return mysql.GetDB(socCommon.DBName, false, common.GetLogger(ctx))
}

// Public functions for FilterRule
func CreateFilterRule(ctx context.Context, rule *RuleTable) (int64, error) {
	return DefaultService.CreateFilterRule(ctx, rule)
}

func DeleteFilterRule(ctx context.Context, ruleID string) error {
	return DefaultService.DeleteFilterRule(ctx, ruleID)
}

func UpdateFilterRuleByRuleID(ctx context.Context, ruleID string, businessID *string, tenantID *TenantIDArray, name *string, desc *string, ruleType *socCommon.FilterRuleType, filterConfig *FilterRuleConfig, dedupConfig *DeduplicationRuleConfig, status *socCommon.FilterRuleStatus, version *int64) error {
	return DefaultService.UpdateFilterRuleByRuleID(ctx, ruleID, businessID, tenantID, name, desc, ruleType, filterConfig, dedupConfig, status, version)
}

func QueryFilterRuleBySeveralConditions(ctx context.Context, filter FilterRuleQueryFilter) ([]*RuleTable, error) {
	return DefaultService.QueryFilterRuleBySeveralConditions(ctx, filter)
}

func QueryFilterRuleCountBySeveralConditions(ctx context.Context, filter FilterRuleQueryFilter) (int64, error) {
	return DefaultService.QueryFilterRuleCountBySeveralConditions(ctx, filter)
}

func QueryLatestFilterRule(ctx context.Context, filter FilterRuleQueryFilter) ([]*RuleTable, error) {
	return DefaultService.QueryLatestFilterRule(ctx, filter)
}

func QueryLatestFilterRuleCount(ctx context.Context, filter FilterRuleQueryFilter) (int64, error) {
	return DefaultService.QueryLatestFilterRuleCount(ctx, filter)
}

