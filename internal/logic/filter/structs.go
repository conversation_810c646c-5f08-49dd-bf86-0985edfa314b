/**
 * @note
 * filter logic structs
 *
 * <AUTHOR>
 * @date 	2025-09-03
 */
package filter

import (
	filterModel "gitlab.docsl.com/security/socv2/soc/internal/model/filter"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"time"
)

// FilterRuleItem 过滤规则信息
type FilterRuleItem struct {
	ID           int64                              `json:"id"`
	RuleID       string                             `json:"ruleID"`
	BusinessID   string                             `json:"businessID"`
	TenantID     *filterModel.TenantIDArray         `json:"tenantID"`
	Name         string                             `json:"name"`
	Desc         string                             `json:"desc"`
	Type         socCommon.FilterRuleType           `json:"type"`
	FilterConfig *filterModel.FilterRuleConfig      `json:"filterConfig"`
	DedupConfig  *filterModel.DeduplicationRuleConfig `json:"dedupConfig"`
	Status       socCommon.FilterRuleStatus         `json:"status"`
	Version      int64                              `json:"version"`
	CreatedAt    time.Time                          `json:"createdAt"`
	UpdatedAt    time.Time                          `json:"updatedAt"`
}
